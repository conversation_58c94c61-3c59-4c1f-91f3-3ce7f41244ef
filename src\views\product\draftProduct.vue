<template>
  <div class="component-container">
    <div
      class="loading"
      v-loading="productLoading"
      v-show="productLoading"
    ></div>
    <el-tabs type="border-card">
      <el-tab-pane label="基础属性">
        <approval-process :approvalData="approvalData"></approval-process>
        <spu
          ref="spu"
          :spuData="spuData"
          :skuData="skuData"
          @reuseSpu="reuseSpu"
        ></spu>
        <sku ref="sku" :skuData="skuData" :sauData="sauData"></sku>
        <label-attr ref="labelAttr" :skuData="skuData" :spuData="spuData" :loadForm="false"
        :showEmpty="spuCategory.type == 'GENERAL_MEDICINE' || spuCategory.type == 'TRADITIONAL_MEDICINE' ? false : true"></label-attr>
        <extended-attr ref="extend" v-if="spuCategory.type == 'GENERAL_MEDICINE' && urlParam.from != 'spuOperate'"></extended-attr>
        <extended-attr2 ref="extend" v-if="spuCategory.type == 'TRADITIONAL_MEDICINE' && urlParam.from != 'spuOperate'"></extended-attr2>
      </el-tab-pane>
    </el-tabs>
    <el-row class="bottom-btns">
      <el-col :span="8" :offset="16" class="text-rt">
        <el-button @click="deleteDraft()">删除</el-button>
        <el-button @click="submit('draft')">保存草稿</el-button>
        <el-button type="primary" @click="submit()">提交</el-button>
        <el-button
          type="primary"
          v-show="$store.getters.spuCategory.type !== 'GENERAL_MEDICINE'"
          @click="submit(false, 'checkSpu')"
          >spu唯一性校验</el-button
        >
      </el-col>
    </el-row>
  </div>
</template>
<script>
// 商品基础共用信息混入对象
import { productMixinBase } from "@/mixin/productMixinBase.js";
import {
  getDarftData, //获取草稿数据
  getProductData, //获取商品信息
  getGoodsDataDetail, // 同步待提交数据
  productAdd, // 商品添加
  getApplyInfo, //审批流信息
  checkSpuSingleApi, //检查SPU唯一性
} from "@/api/product";

export default {
  name: "",
  mixins: [productMixinBase],
  data() {
    return {};
  },
  computed: {
    spuCategory() {
      return this.$store.state.product.spuCategory;
    },
  },
  created() {
    // 设置商品操作类型为草稿
    this.$store.commit("product/SET_OPERATION_TYPE", "draft");
    // 获取审批流信息
    this.getApplyInfo();
    this.getDarftInfo();
    // 检测页面数据值修改变化
    // this.$bus.$on("productChange", res => {
    //   console.log("页面被改动了~");
    //   this.editState = res;
    // });
  },
  mounted() {},
  methods: {
    async getDarftInfo() {
      let { applyCode, spuCode } = this.urlParam;
      let res = await getDarftData({ applyCode, spuCode });
      // setTimeout(() => {
      //   // 这里关闭太早会存在页面数据没渲染完的闪动，暂时延迟处理
      //   this.productLoading = false;
      // }, 600);
      // console.log(res, "草稿");
      this.spuData = Object.freeze(res.data.spu);
      this.skuData = Object.freeze(res.data.sku);
      this.sauData = Object.freeze(res.data.sau);
    },
    /**
     * @description:spu复用
     * @param {string} spucode 复用的spucode
     * @return:
     */
    async reuseSpu(spuCode) {
      // console.log(spucode);
      this.productLoading = true;
      // 设置商品操作类型为复用
      this.$store.commit("product/SET_OPERATION_TYPE", "reuse");
      // 查询下属所有
      let spuInfo = await getProductData({ spuCode }, "all");
      // console.log(spuInfo);
      this.spuData = Object.freeze(spuInfo.data.spu);
      this.skuData = Object.freeze(spuInfo.data.sku);
      this.sauData = Object.freeze(spuInfo.data.sau);
      // setTimeout(() => {
      //   // 这里关闭太早会存在页面数据没渲染完的闪动，暂时延迟处理
      //   this.productLoading = false;
      // }, 600);
    },
    /**
     * @description:商品数据提交
     * @param {string} isDraft 存在，保存草稿，不存在，提交
     * @param {string} checkSpuSingle 是否为检查SPU唯一触发
     * @return:
     */
    async submit(isDraft, checkSpuSingle) {
      let spu = await this.$refs.spu.getSpuData(isDraft);
      let skuData = this.$refs.sku.getSkuData(isDraft);
      let sau = {}
      let extendData = {};
      if (this.spuCategory.type == "GENERAL_MEDICINE") {
        extendData = this.$refs.extend.getExtendData();
      }
      if (!spu.state || !skuData || !extendData) {
        // 未通过 SPU表单校验
        return;
      }
      let spuData = spu.data;
      if (checkSpuSingle) {
        this.productLoading = true;
        let res = await checkSpuSingleApi(spuData);
        // console.log(res);
        if (res.success) {
          this.$message.success("spu唯一性校验通过");
        } else {
          this.$message.error(res.retMsg);
        }
        this.productLoading = false;
        return false;
      }
      // console.log("extend:", extendData);
      let submitData = this.formatSubmitData(
        _.cloneDeep(spuData),
        _.cloneDeep(skuData),
        _.cloneDeep(sau),
        _.cloneDeep(extendData)
      );
      //操作类型,
      // (0:保存草稿,
      // 1:新增spu,
      // 2:属性修改,
      // 3:商品合并,
      // 4:商品停用,
      // 5:三方同步,
      // 6:批量修改,
      // 7:草稿删除,
      // 8:预首营修改)
      if (isDraft) {
        //保存草稿
        submitData.operType = 0;
      } else {
        //新增
        submitData.operType = 1;
      }
      submitData.applyCode = this.urlParam.applyCode;
      this.productLoading = true;
      console.log(submitData, "草稿页面--提交--");
      let res = await productAdd(submitData);
      this.productLoading = false;
      console.log(res);
      if (res.success) {
        this.$message.success("提交成功");
        parent.CreateTab(
          "../static/dist/index.html#/product/productList",
          "商品列表",
          true
        );
        parent.CloseTab("../static/dist/index.html#/product/draftProduct"); // 关闭当前页面
      } else {
        this.$message.error(res.retMsg);
      }
    },
    deleteDraft() {
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let res = await productAdd({
          operType: 7,
          applyCode: this.urlParam.applyCode,
        });
        if (res && res.retCode == 0) {
          parent.CreateTab(
            "../static/dist/index.html#/product/productList",
            "商品列表",
            true
          );
          parent.CloseTab("../static/dist/index.html#/product/draftProduct"); // 关闭当前页面
        } else {
          this.$message.error(res.retMsg);
        }
      });
    },
    async getApplyInfo() {
      let param = {
        selectType: 0, // 查询类型, (0:草稿, 1:新增, 2:属性修改,3:审核或驳回修改,4:查询详情)
        applyCode: this.urlParam.applyCode, //单据编号
        productCode: "", //商品编码
        productType: 1, //商品类型(1-SPU;2-SKU;3-SAU)
        spuCode: "", //SPU编码
      };
      let res = await getApplyInfo(param);
      if (res.success) {
        this.approvalData = res.data;
      } else {
        this.$message.error(res.retMsg);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.component-container {
  padding-top: 0px;
  .loading {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100;
  }
  /deep/ .el-tabs__header {
    border-bottom: none;
    background-color: #f2f2f2;
  }
  .el-tabs--border-card {
    border: none;
    box-shadow: none;
    /deep/ .el-tabs__content {
      padding-bottom: 80px;
    }
  }

  .bottom-btns {
    background: #fff;
    width: 100%;
    position: fixed;
    bottom: 0;
    padding: 15px;
    z-index: 10;
  }
}
</style>
