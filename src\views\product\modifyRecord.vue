<template>
  <div class="record-container">
    <div
      class="partition-content"
      style="height: 20px; background: #f0f2f5; padding-left: -20px"
    ></div>
    <!-- 修改记录 列表 -->
    <div class="content">
      <h4>修改记录</h4>
      <vxe-table
        border
        highlight-hover-row
        auto-resize
        resizable
        max-height="500"
        align="center"
        :tooltip-config="{ enterable: false }"
        :data="recordTableData"
        ref="recordTable"
      >
        <vxe-table-column
          type="index"
          title="序号"
          width="60"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="createTime"
          title="申请时间"
          min-width="120"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">{{ row.createTime | dateTime }}</template>
        </vxe-table-column>
        <vxe-table-column
          field="applyUserName"
          title="申请人"
          min-width="80"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="affiliation"
          title="所属机构"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="productCode"
          title="商品编码"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="approvalProcess"
          title="当前审批流程"
          min-width="120"
          show-header-overflow
          show-overflow
        >
          <!-- 0:'商品新增'1:'商品修改'2:'商品合并'3:'批量停用'4:'批量扩展'5:'批量修改' 6:'预首营审核'7:'批量新增' 8:'批量用药指导'default:'商品新增' -->
          <template v-slot="{ row }">{{
            {
              0: "商品新增",
              1: "商品修改",
              2: "商品合并",
              3: "批量停用",
              4: "批量扩展",
              5: "批量修改",
              6: "预首营审核",
              7: "批量新增",
              8: "批量用药指导",
              10: "取消合并",
              13: "商品纠错",
              14: "批量税率更新",
              16: "移动商品",
              21: "副商品新增"
            }[row.approvalProcess]
          }}</template>
        </vxe-table-column>
        <vxe-table-column
          field="applyContent"
          title="申请内容"
          min-width="200"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">
            <!-- 去 商品查看明细页 -->
            <template
              v-if="row.approvalProcess == 0 || row.approvalProcess == 7"
            >
              <el-link
                :underline="false"
                type="primary"
                @click="goToDetail(row)"
                >查看申请明细</el-link
              >
            </template>
            <template v-else>{{ row.applyContent }}</template>
            <div
              v-if="
                row.contentRecordBusinessDtoList &&
                row.contentRecordBusinessDtoList.length > 0
              "
            >
              <el-link
                :underline="false"
                type="primary"
                @click="previewMore(row.contentRecordBusinessDtoList)"
                >查看更多</el-link
              >
            </div>
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="reviewStatus"
          title="审核状态"
          min-width="120"
          show-header-overflow
          show-overflow
        >
          <!-- 0:'运营中心审核中'1:'质管总部审核中'2:'审核驳回'3:'审核通过'4:'审核不通过'5:'运营中心审核中',6:'审核中' -->
          <template v-slot="{ row }">{{
            [
              "运营中心审核中",
              "质管总部审核中",
              "审核驳回",
              "审核通过",
              "审核不通过",
              "财务部审核中",
              "审核中"
            ][row.reviewStatus]
          }}</template>
        </vxe-table-column>
        <vxe-table-column
          field="lastReviewerName"
          title="最后审核人"
          min-width="100"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="updateTime"
          title="最后审核时间"
          min-width="120"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">{{ row.updateTime | dateTime }}</template>
        </vxe-table-column>
        <vxe-table-column
          field="goodsReviewId"
          title="审批流程"
          min-width="80"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">
            <el-link
              :underline="false"
              type="primary"
              @click="viewApprovalProcess(row.goodsReviewId,row.applyCode)"
              >查看</el-link
            >
          </template>
        </vxe-table-column>
      </vxe-table>
    </div>

    <!-- 修改记录 对话框 -->
    <el-dialog
      title="修改记录"
      :visible.sync="recordDetail.dlgVisible"
      width="800px"
    >
      <vxe-table
        border
        highlight-hover-row
        resizable
        max-height="500"
        align="center"
        :tooltip-config="{ enterable: false }"
        :data="recordDetail.tableData"
      >
        <vxe-table-column
          type="index"
          title="序号"
          width="60"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column field="createTime" title="操作时间" min-width="120">
          <template v-slot="{ row }">{{ row.createTime | dateTime }}</template>
        </vxe-table-column>
        <vxe-table-column
          field="createUser"
          title="操作人"
          min-width="80"
        ></vxe-table-column>
        <vxe-table-column
          field="operationNode"
          title="操作节点"
          min-width="100"
        ></vxe-table-column>
        <vxe-table-column
          field="applyContent"
          title="修改明细"
          min-width="200"
        ></vxe-table-column>
      </vxe-table>
    </el-dialog>

    <!-- 查看审批流程 对话框 -->
    <el-dialog
      title="查看审批流程"
      :visible.sync="approvalProcess.dlgVisible"
      width="800px"
    >
      <vxe-table
        border
        highlight-hover-row
        resizable
        max-height="500"
        align="center"
        :tooltip-config="{ enterable: false }"
        :data="approvalProcess.tableData"
      >
        <vxe-table-column
          type="index"
          title="序号"
          width="60"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column field="updateTime" title="审核时间" min-width="130">
          <template v-slot="{ row }">{{ row.updateTime | dateTime }}</template>
        </vxe-table-column>
        <vxe-table-column
          field="reviewerName"
          title="审核人"
          min-width="80"
        ></vxe-table-column>
        <vxe-table-column
          field="reviewNode"
          title="审核节点"
          min-width="120"
        ></vxe-table-column>
        <vxe-table-column
          field="resultOfReviewStr"
          title="审核结果"
          min-width="100"
        ></vxe-table-column>
        <vxe-table-column
          field="reviewOpinion"
          title="审核意见"
          min-width="100"
        ></vxe-table-column>
      </vxe-table>
    </el-dialog>
  </div>
</template>

<script>
import { parseTimestamp } from "@/utils/index.js";
import { getModifyRecord, getApprovalProcess } from "@/api/product";
export default {
  name: "",
  components: {},
  filters: {
    dateTime: function (value) {
      if (!value) return "";
      return parseTimestamp(value);
    },
  },
  props: {
    recordData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      recordTableData: [],
      // 修改记录 明细
      recordDetail: {
        dlgVisible: false,
        tableData: [],
      },
      // 查看审批流程
      approvalProcess: {
        dlgVisible: false,
        tableData: [],
      },
    };
  },
  computed: {
    // 操作类型
    operationType() {
      return this.$store.getters.operationType;
    },

    // 路由参数
    urlParam() {
      return this.$route.query;
    },
  },
  watch: {
    recordData(val) {
      // console.log(val, "---修改记录数据");
      if (val && val.length) {
        this.recordTableData = val;
      }
    },
  },
  created() {},
  mounted() {},
  methods: {
    //查看 更多明细
    previewMore(data) {
      this.recordDetail.dlgVisible = true;
      this.recordDetail.tableData = data;
    },

    // 查看 审批流程
    viewApprovalProcess(id,applyCode) {
      getApprovalProcess(id,applyCode).then((data) => {
        this.approvalProcess.tableData = data;
        this.approvalProcess.dlgVisible = true;
      });
    },

    // 跳转到明细页
    goToDetail(row) {
      console.log(row, this.urlParam, "--跳转");
      const copyUrlParam = JSON.parse(JSON.stringify(this.urlParam));
      delete copyUrlParam.from;
      let a = JSON.stringify(copyUrlParam)
      let b = a.replace(/:/g, "=");
      let c = b.replace(/,/g, "&");
      let d = c.replace(/"/g, "");
      let e = d.replace(/{/g, "");
      let str = e.replace(/}/g, "");
      // console.log(str);
      window.parent.CreateTab(
        `../static/dist/index.html#/product/detailProduct?${str}&record=hide`,
        "商品查看明细页",
        true
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  padding: 0 30px;
  padding-bottom: 80px;
  h4 {
    font-weight: 600;
    margin-bottom: 22px;
    margin-top: 10px;
    border-bottom: 1px solid #e4e4eb;
    line-height: 50px;
    padding-left: 20px;
    position: relative;
  }
}
</style>